package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import android.util.Log
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.phad.chatapp.features.scheduling.models.OptimizedVolunteerAssignment
import com.phad.chatapp.features.scheduling.models.GroupAvailability
import com.phad.chatapp.features.scheduling.models.ScheduleReferenceData
import com.phad.chatapp.features.scheduling.models.SubjectPreset
import com.phad.chatapp.features.scheduling.models.VolunteerDetails
import com.phad.chatapp.features.scheduling.ui.theme.DarkBackground
import com.phad.chatapp.features.scheduling.ui.theme.NeutralCardSurface
import com.phad.chatapp.features.scheduling.ui.theme.YellowAccent
import com.phad.chatapp.features.scheduling.firebase.FirestoreCollection
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

data class ScheduleGridData(
    val referenceData: ScheduleReferenceData,
    val volunteerAssignments: List<OptimizedVolunteerAssignment>,
    val groupAvailability: List<GroupAvailability>,
    val subjectPreset: SubjectPreset
)

data class GridCell(
    val dayIndex: Int,
    val slotIndex: Int,
    val volunteerName: String,
    val volunteerRollNo: String,
    val volunteerGroup: String,
    val subjectCode: String? = null,
    val isHighlighted: Boolean = false,
    val scheduleId: String = "", // Track which schedule this cell belongs to
    val scheduleName: String = "", // Track schedule name for display
    // Enhanced volunteer data from generateSchedule collection
    val interviewScore: Int = 0,
    val subjectPreference1: String = "",
    val subjectPreference2: String = "",
    val subjectPreference3: String = ""
)

@Composable
fun SubjectAssignmentGridScreen(
    navController: NavController,
    scheduleIds: String,
    pairingData: String
) {
    var isLoading by remember { mutableStateOf(true) }
    var scheduleData by remember { mutableStateOf<List<ScheduleGridData>>(emptyList()) }
    var selectedCell by remember { mutableStateOf<GridCell?>(null) }
    var highlightedCells by remember { mutableStateOf<Set<Pair<Int, Int>>>(emptySet()) }
    var showDetailsDialog by remember { mutableStateOf(false) }
    var detailsCell by remember { mutableStateOf<GridCell?>(null) }
    var isSwapping by remember { mutableStateOf(false) }

    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()

    // Parse navigation parameters
    val scheduleIdList = scheduleIds.split(",").filter { it.isNotBlank() }
    val pairings = pairingData.split("|").associate { pair ->
        val parts = pair.split(":")
        if (parts.size == 2) parts[0] to parts[1] else "" to ""
    }.filterKeys { it.isNotEmpty() }

    Log.d("SubjectAssignmentGrid", "Received scheduleIds: $scheduleIds")
    Log.d("SubjectAssignmentGrid", "Received pairingData: $pairingData")
    Log.d("SubjectAssignmentGrid", "Parsed schedule list: $scheduleIdList")
    Log.d("SubjectAssignmentGrid", "Parsed pairings: $pairings")

    // Load schedule data
    LaunchedEffect(scheduleIds, pairingData) {
        loadScheduleData(scheduleIdList, pairings) { data ->
            scheduleData = data
            isLoading = false
            Log.d("SubjectAssignmentGrid", "Data loading completed. Loaded ${data.size} schedules")
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(DarkBackground)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp, vertical = 8.dp)
        ) {
            // Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(
                    onClick = { navController.navigateUp() },
                    modifier = Modifier.size(48.dp)
                ) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Subject Assignment Grid",
                    style = MaterialTheme.typography.titleLarge,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .weight(1f)
                        .padding(start = 8.dp)
                )
            }

            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = YellowAccent)
                }
            } else {
                if (scheduleData.isNotEmpty()) {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(24.dp),
                        contentPadding = PaddingValues(vertical = 16.dp)
                    ) {
                        scheduleData.forEach { schedule ->
                            item {
                                ScheduleGridCard(
                                    scheduleData = schedule,
                                    selectedCell = selectedCell,
                                    highlightedCells = highlightedCells,
                                    onCellLongPress = { cell ->
                                        selectedCell = cell
                                        // Use inter-schedule swap validation for better functionality
                                        highlightedCells = findValidSwapCellsAcrossSchedules(cell, scheduleData)
                                    },
                                    onCellClick = { cell ->
                                        if (selectedCell != null && highlightedCells.contains(cell.dayIndex to cell.slotIndex)) {
                                            // Perform swap (supports both intra and inter-schedule)
                                            isSwapping = true
                                            coroutineScope.launch {
                                                val success = performVolunteerSwapAcrossSchedules(selectedCell!!, cell, scheduleData)
                                                isSwapping = false
                                                if (success) {
                                                    val swapType = if (selectedCell!!.scheduleName == cell.scheduleName) "intra-schedule" else "inter-schedule"
                                                    snackbarHostState.showSnackbar("Volunteers swapped successfully ($swapType)")
                                                    // Reload data to reflect changes
                                                    loadScheduleData(scheduleIdList, pairings) { newData ->
                                                        scheduleData = newData
                                                    }
                                                } else {
                                                    snackbarHostState.showSnackbar("Failed to swap volunteers")
                                                }
                                                selectedCell = null
                                                highlightedCells = emptySet()
                                            }
                                        } else {
                                            // Show details dialog
                                            detailsCell = cell
                                            showDetailsDialog = true
                                            selectedCell = null
                                            highlightedCells = emptySet()
                                        }
                                    }
                                )
                            }
                        }
                    }
                } else {
                    // Empty state with debug info
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "No schedule data available",
                                color = Color.White,
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = "Schedule IDs: $scheduleIdList",
                                color = Color(0xFFB0B0B0),
                                style = MaterialTheme.typography.bodySmall
                            )
                            Text(
                                text = "Pairings: $pairings",
                                color = Color(0xFFB0B0B0),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
        }

        // SnackbarHost for user feedback
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )

        // Loading overlay for swapping
        if (isSwapping) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.7f)),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    CircularProgressIndicator(color = YellowAccent)
                    Text(
                        text = "Swapping volunteers...",
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
    }

    // Volunteer details dialog
    if (showDetailsDialog && detailsCell != null) {
        VolunteerDetailsDialog(
            cell = detailsCell!!,
            scheduleData = scheduleData.firstOrNull(),
            onDismiss = { showDetailsDialog = false }
        )
    }
}

@Composable
fun ScheduleGridCard(
    scheduleData: ScheduleGridData,
    selectedCell: GridCell?,
    highlightedCells: Set<Pair<Int, Int>>,
    onCellLongPress: (GridCell) -> Unit,
    onCellClick: (GridCell) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = NeutralCardSurface)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Schedule name header
            Text(
                text = "Schedule: ${scheduleData.referenceData.schoolName}",
                style = MaterialTheme.typography.titleMedium,
                color = YellowAccent,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Grid table
            ScheduleTable(
                scheduleData = scheduleData,
                selectedCell = selectedCell,
                highlightedCells = highlightedCells,
                onCellLongPress = onCellLongPress,
                onCellClick = onCellClick
            )
        }
    }
}

@Composable
fun ScheduleTable(
    scheduleData: ScheduleGridData,
    selectedCell: GridCell?,
    highlightedCells: Set<Pair<Int, Int>>,
    onCellLongPress: (GridCell) -> Unit,
    onCellClick: (GridCell) -> Unit
) {
    val referenceData = scheduleData.referenceData
    val assignments = scheduleData.volunteerAssignments
    
    // Create grid data structure
    val gridCells = mutableMapOf<Pair<Int, Int>, GridCell>()
    Log.d("SubjectAssignmentGrid", "Creating grid with ${assignments.size} assignments")

    assignments.forEach { assignment ->
        val key = assignment.dayIndex to assignment.slotIndex
        Log.d("SubjectAssignmentGrid", "Adding cell at ($key) for ${assignment.volunteerName}")
        gridCells[key] = GridCell(
            dayIndex = assignment.dayIndex,
            slotIndex = assignment.slotIndex,
            volunteerName = assignment.volunteerName,
            volunteerRollNo = assignment.volunteerRollNo.takeLast(4),
            volunteerGroup = assignment.volunteerGroup,
            subjectCode = getSubjectForCell(assignment, scheduleData.subjectPreset),
            isHighlighted = highlightedCells.contains(key),
            scheduleId = "", // Will be set when we have access to schedule ID
            scheduleName = scheduleData.referenceData.schoolName,
            // Include enhanced volunteer data from generateSchedule collection
            interviewScore = assignment.interviewScore,
            subjectPreference1 = assignment.subjectPreference1,
            subjectPreference2 = assignment.subjectPreference2,
            subjectPreference3 = assignment.subjectPreference3
        )
    }

    Log.d("SubjectAssignmentGrid", "Grid created with ${gridCells.size} cells")

    Column {
        // Header row with time slots
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            // Empty cell for day column
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(40.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Day/Time",
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    style = MaterialTheme.typography.bodySmall
                )
            }
            
            // Time slot headers
            referenceData.timeSlotNames.forEach { timeSlot ->
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(40.dp)
                        .background(Color(0xFF2A2A2A), RoundedCornerShape(4.dp)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = timeSlot,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        style = MaterialTheme.typography.bodySmall,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        // Day rows
        referenceData.dayNames.forEachIndexed { dayIndex, dayName ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                // Day name cell
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(60.dp)
                        .background(Color(0xFF2A2A2A), RoundedCornerShape(4.dp)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = dayName,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                // Time slot cells
                referenceData.timeSlotNames.forEachIndexed { slotIndex, _ ->
                    val cell = gridCells[dayIndex to slotIndex]
                    if (cell != null) {
                        VolunteerCell(
                            cell = cell,
                            isSelected = selectedCell?.dayIndex == dayIndex && selectedCell?.slotIndex == slotIndex,
                            onLongPress = { onCellLongPress(cell) },
                            onClick = { onCellClick(cell) },
                            modifier = Modifier.weight(1f)
                        )
                    } else {
                        // Empty cell - clickable for details
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(60.dp)
                                .background(Color(0xFF1A1A1A), RoundedCornerShape(4.dp))
                                .clip(RoundedCornerShape(4.dp))
                                .pointerInput(Unit) {
                                    detectTapGestures(
                                        onTap = {
                                            // Create empty cell for details
                                            val emptyCell = GridCell(
                                                dayIndex = dayIndex,
                                                slotIndex = slotIndex,
                                                volunteerName = "",
                                                volunteerRollNo = "",
                                                volunteerGroup = "",
                                                scheduleId = "",
                                                scheduleName = scheduleData.referenceData.schoolName,
                                                interviewScore = 0,
                                                subjectPreference1 = "",
                                                subjectPreference2 = "",
                                                subjectPreference3 = ""
                                            )
                                            onCellClick(emptyCell)
                                        }
                                    )
                                }
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(2.dp))
        }
    }
}

@Composable
fun VolunteerCell(
    cell: GridCell,
    isSelected: Boolean,
    onLongPress: () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when {
        isSelected -> YellowAccent.copy(alpha = 0.8f)
        cell.isHighlighted -> YellowAccent.copy(alpha = 0.4f)
        else -> Color(0xFF2A2A2A)
    }

    Box(
        modifier = modifier
            .height(60.dp)
            .background(backgroundColor, RoundedCornerShape(4.dp))
            .clip(RoundedCornerShape(4.dp))
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = { onLongPress() },
                    onTap = { onClick() }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = if (cell.subjectCode != null) Alignment.Start else Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(4.dp)
        ) {
            if (cell.subjectCode != null) {
                // After subject assignment - left-aligned volunteer info, right-aligned subject
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column {
                        Text(
                            text = cell.volunteerName.split(" ").firstOrNull() ?: "",
                            color = if (isSelected) Color.Black else Color.White,
                            style = MaterialTheme.typography.bodySmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = cell.volunteerRollNo,
                            color = if (isSelected) Color.Black else Color(0xFFB0B0B0),
                            style = MaterialTheme.typography.labelSmall
                        )
                    }

                    Text(
                        text = cell.subjectCode!!,
                        color = if (isSelected) Color.Black else YellowAccent,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Bold
                    )
                }
            } else {
                // Initially - center-aligned volunteer info only
                Text(
                    text = cell.volunteerName.split(" ").firstOrNull() ?: "",
                    color = if (isSelected) Color.Black else Color.White,
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = cell.volunteerRollNo,
                    color = if (isSelected) Color.Black else Color(0xFFB0B0B0),
                    style = MaterialTheme.typography.labelSmall,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

// Helper functions
private suspend fun loadScheduleData(
    scheduleIds: List<String>,
    pairings: Map<String, String>,
    onResult: (List<ScheduleGridData>) -> Unit
) {
    try {
        Log.d("SubjectAssignmentGrid", "Loading schedule data for IDs: $scheduleIds")
        Log.d("SubjectAssignmentGrid", "Pairings: $pairings")

        val db = FirebaseFirestore.getInstance()
        val scheduleDataList = mutableListOf<ScheduleGridData>()

        for (scheduleId in scheduleIds) {
            val subjectPresetId = pairings[scheduleId] ?: continue
            Log.d("SubjectAssignmentGrid", "Processing schedule: $scheduleId with subject preset: $subjectPresetId")

            // Load schedule data
            val scheduleDoc = db.collection(FirestoreCollection.GENERATED_SCHEDULES)
                .document(scheduleId)
                .get()
                .await()

            // Load subject preset data
            val subjectPresetDoc = db.collection(FirestoreCollection.SUBJECT_PRESETS)
                .document(subjectPresetId)
                .get()
                .await()

            Log.d("SubjectAssignmentGrid", "Schedule doc exists: ${scheduleDoc.exists()}")
            Log.d("SubjectAssignmentGrid", "Subject preset doc exists: ${subjectPresetDoc.exists()}")

            if (scheduleDoc.exists() && subjectPresetDoc.exists()) {
                Log.d("SubjectAssignmentGrid", "Schedule document data keys: ${scheduleDoc.data?.keys}")
                val scheduleData = parseScheduleDocument(scheduleDoc.data!!)
                val subjectPreset = parseSubjectPresetDocument(subjectPresetDoc.data!!)

                Log.d("SubjectAssignmentGrid", "Parsed ${scheduleData.second.size} volunteer assignments")
                Log.d("SubjectAssignmentGrid", "Reference data: days=${scheduleData.first.dayNames}, slots=${scheduleData.first.timeSlotNames}")

                scheduleDataList.add(
                    ScheduleGridData(
                        referenceData = scheduleData.first,
                        volunteerAssignments = scheduleData.second,
                        groupAvailability = scheduleData.third,
                        subjectPreset = subjectPreset
                    )
                )
            }
        }

        Log.d("SubjectAssignmentGrid", "Loaded ${scheduleDataList.size} schedule data items")
        onResult(scheduleDataList)
    } catch (e: Exception) {
        Log.e("SubjectAssignmentGrid", "Error loading schedule data", e)
        onResult(emptyList())
    }
}

private fun parseScheduleDocument(data: Map<String, Any>): Triple<ScheduleReferenceData, List<OptimizedVolunteerAssignment>, List<GroupAvailability>> {
    Log.d("SubjectAssignmentGrid", "Parsing schedule document with keys: ${data.keys}")

    val referenceData = (data["referenceData"] as? Map<String, Any>)?.let { ref ->
        Log.d("SubjectAssignmentGrid", "Reference data keys: ${ref.keys}")
        ScheduleReferenceData(
            dayNames = (ref["dayNames"] as? List<String>) ?: emptyList(),
            timeSlotNames = (ref["timeSlotNames"] as? List<String>) ?: emptyList(),
            schoolName = data["name"] as? String ?: "",
            totalDays = (ref["totalDays"] as? Number)?.toInt() ?: 0,
            totalSlots = (ref["totalSlots"] as? Number)?.toInt() ?: 0
        )
    } ?: ScheduleReferenceData()

    // Try both data structures - optimizedAssignments (flat) and optimizedSlotAssignments (grouped)
    val assignments = mutableListOf<OptimizedVolunteerAssignment>()

    // First try the flat structure (optimizedAssignments)
    val flatAssignments = data["optimizedAssignments"] as? List<Map<String, Any>>
    Log.d("SubjectAssignmentGrid", "Flat assignments found: ${flatAssignments != null}, size: ${flatAssignments?.size}")

    if (flatAssignments != null) {
        assignments.addAll(flatAssignments.map { assignment ->
            Log.d("SubjectAssignmentGrid", "Processing assignment: ${assignment.keys}")
            OptimizedVolunteerAssignment(
                volunteerName = assignment["volunteerName"] as? String ?: "",
                volunteerRollNo = assignment["volunteerRollNo"] as? String ?: assignment["volunteerRollNumber"] as? String ?: "",
                volunteerGroup = assignment["volunteerGroup"] as? String ?: "",
                dayIndex = (assignment["dayIndex"] as? Number)?.toInt() ?: 0,
                slotIndex = (assignment["slotIndex"] as? Number)?.toInt() ?: 0,
                interviewScore = (assignment["interviewScore"] as? Number)?.toInt() ?: 0,
                subjectPreference1 = assignment["subjectPreference1"] as? String ?: "",
                subjectPreference2 = assignment["subjectPreference2"] as? String ?: "",
                subjectPreference3 = assignment["subjectPreference3"] as? String ?: ""
            )
        })
    } else {
        // Fallback to grouped structure (optimizedSlotAssignments)
        val groupedAssignments = data["optimizedSlotAssignments"] as? List<Map<String, Any>>
        Log.d("SubjectAssignmentGrid", "Grouped assignments found: ${groupedAssignments != null}, size: ${groupedAssignments?.size}")

        groupedAssignments?.forEach { slotGroup ->
            val dayIndex = (slotGroup["dayIndex"] as? Number)?.toInt() ?: 0
            val slotIndex = (slotGroup["slotIndex"] as? Number)?.toInt() ?: 0
            val slotAssignments = slotGroup["assignments"] as? List<Map<String, Any>> ?: emptyList()

            slotAssignments.forEach { assignment ->
                assignments.add(
                    OptimizedVolunteerAssignment(
                        volunteerName = assignment["volunteerName"] as? String ?: "",
                        volunteerRollNo = assignment["volunteerRollNo"] as? String ?: assignment["volunteerRollNumber"] as? String ?: "",
                        volunteerGroup = assignment["volunteerGroup"] as? String ?: "",
                        dayIndex = dayIndex,
                        slotIndex = slotIndex,
                        interviewScore = (assignment["interviewScore"] as? Number)?.toInt() ?: 0,
                        subjectPreference1 = assignment["subjectPreference1"] as? String ?: "",
                        subjectPreference2 = assignment["subjectPreference2"] as? String ?: "",
                        subjectPreference3 = assignment["subjectPreference3"] as? String ?: ""
                    )
                )
            }
        }
    }

    Log.d("SubjectAssignmentGrid", "Total assignments parsed: ${assignments.size}")

    val groupAvailability = (data["groupAvailability"] as? List<Map<String, Any>>)?.map { availability ->
        GroupAvailability(
            dayIndex = (availability["dayIndex"] as? Number)?.toInt() ?: 0,
            slotIndex = (availability["slotIndex"] as? Number)?.toInt() ?: 0,
            availableGroups = (availability["availableGroups"] as? List<String>) ?: emptyList()
        )
    } ?: emptyList()

    return Triple(referenceData, assignments, groupAvailability)
}

private fun parseSubjectPresetDocument(data: Map<String, Any>): SubjectPreset {
    return SubjectPreset(
        id = "",
        name = data["name"] as? String ?: "",
        subjects = (data["subjects"] as? Map<String, Any>)?.mapValues {
            (it.value as? Number)?.toInt() ?: 0
        } ?: emptyMap()
    )
}

private fun getSubjectForCell(assignment: OptimizedVolunteerAssignment, subjectPreset: SubjectPreset): String? {
    // For now, return null - subject assignment logic will be implemented later
    // This is where you would determine which subject should be assigned to this cell
    // based on the subject preset and assignment algorithm
    return null
}

private fun findValidSwapCells(selectedCell: GridCell, scheduleData: ScheduleGridData): Set<Pair<Int, Int>> {
    val validCells = mutableSetOf<Pair<Int, Int>>()

    // Find the selected volunteer's available groups
    val selectedVolunteerAvailability = getVolunteerAvailableGroups(selectedCell, scheduleData)

    // Check each other cell for swap validity
    scheduleData.volunteerAssignments.forEach { assignment ->
        if (assignment.dayIndex != selectedCell.dayIndex || assignment.slotIndex != selectedCell.slotIndex) {
            val otherCell = GridCell(
                dayIndex = assignment.dayIndex,
                slotIndex = assignment.slotIndex,
                volunteerName = assignment.volunteerName,
                volunteerRollNo = assignment.volunteerRollNo,
                volunteerGroup = assignment.volunteerGroup,
                scheduleId = "",
                scheduleName = scheduleData.referenceData.schoolName
            )

            val otherVolunteerAvailability = getVolunteerAvailableGroups(otherCell, scheduleData)

            // Check if swap is valid
            if (canSwapVolunteers(selectedCell, otherCell, selectedVolunteerAvailability, otherVolunteerAvailability, scheduleData)) {
                validCells.add(assignment.dayIndex to assignment.slotIndex)
            }
        }
    }

    return validCells
}

// Enhanced function to find valid swap cells across all schedules
private fun findValidSwapCellsAcrossSchedules(selectedCell: GridCell, allScheduleData: List<ScheduleGridData>): Set<Pair<Int, Int>> {
    val validCells = mutableSetOf<Pair<Int, Int>>()

    // Find the schedule containing the selected cell
    val selectedSchedule = allScheduleData.find { schedule ->
        schedule.referenceData.schoolName == selectedCell.scheduleName
    } ?: return validCells

    // Find the selected volunteer's available groups
    val selectedVolunteerAvailability = getVolunteerAvailableGroups(selectedCell, selectedSchedule)

    // Check cells in all schedules for swap validity
    allScheduleData.forEach { scheduleData ->
        scheduleData.volunteerAssignments.forEach { assignment ->
            // Skip the selected cell itself
            if (!(assignment.dayIndex == selectedCell.dayIndex &&
                  assignment.slotIndex == selectedCell.slotIndex &&
                  scheduleData.referenceData.schoolName == selectedCell.scheduleName)) {

                val otherCell = GridCell(
                    dayIndex = assignment.dayIndex,
                    slotIndex = assignment.slotIndex,
                    volunteerName = assignment.volunteerName,
                    volunteerRollNo = assignment.volunteerRollNo,
                    volunteerGroup = assignment.volunteerGroup,
                    scheduleId = "",
                    scheduleName = scheduleData.referenceData.schoolName
                )

                val otherVolunteerAvailability = getVolunteerAvailableGroups(otherCell, scheduleData)

                // Check if swap is valid (cross-schedule validation)
                if (canSwapVolunteersAcrossSchedules(selectedCell, otherCell, selectedVolunteerAvailability, otherVolunteerAvailability, selectedSchedule, scheduleData)) {
                    validCells.add(assignment.dayIndex to assignment.slotIndex)
                }
            }
        }
    }

    return validCells
}

private fun getVolunteerAvailableGroups(cell: GridCell, scheduleData: ScheduleGridData): List<String> {
    // This would typically come from volunteer availability data
    // For now, return the volunteer's own group as available
    return listOf(cell.volunteerGroup)
}

private fun canSwapVolunteers(
    cell1: GridCell,
    cell2: GridCell,
    cell1Availability: List<String>,
    cell2Availability: List<String>,
    scheduleData: ScheduleGridData
): Boolean {
    // Get the groups required for each cell's time slot
    val cell1RequiredGroups = scheduleData.groupAvailability
        .find { it.dayIndex == cell1.dayIndex && it.slotIndex == cell1.slotIndex }
        ?.availableGroups ?: emptyList()

    val cell2RequiredGroups = scheduleData.groupAvailability
        .find { it.dayIndex == cell2.dayIndex && it.slotIndex == cell2.slotIndex }
        ?.availableGroups ?: emptyList()

    // Check if cell1's volunteer can work in cell2's slot and vice versa
    val cell1VolunteerCanWorkInCell2 = cell1Availability.any { it in cell2RequiredGroups }
    val cell2VolunteerCanWorkInCell1 = cell2Availability.any { it in cell1RequiredGroups }

    return cell1VolunteerCanWorkInCell2 && cell2VolunteerCanWorkInCell1
}

// Enhanced function for cross-schedule volunteer swapping validation
private fun canSwapVolunteersAcrossSchedules(
    cell1: GridCell,
    cell2: GridCell,
    cell1Availability: List<String>,
    cell2Availability: List<String>,
    schedule1Data: ScheduleGridData,
    schedule2Data: ScheduleGridData
): Boolean {
    // Get the groups required for each cell's time slot in their respective schedules
    val cell1RequiredGroups = schedule1Data.groupAvailability
        .find { it.dayIndex == cell1.dayIndex && it.slotIndex == cell1.slotIndex }
        ?.availableGroups ?: emptyList()

    val cell2RequiredGroups = schedule2Data.groupAvailability
        .find { it.dayIndex == cell2.dayIndex && it.slotIndex == cell2.slotIndex }
        ?.availableGroups ?: emptyList()

    // Check if cell1's volunteer can work in cell2's slot and vice versa
    val cell1VolunteerCanWorkInCell2 = cell1Availability.any { it in cell2RequiredGroups }
    val cell2VolunteerCanWorkInCell1 = cell2Availability.any { it in cell1RequiredGroups }

    return cell1VolunteerCanWorkInCell2 && cell2VolunteerCanWorkInCell1
}

private suspend fun performVolunteerSwap(selectedCell: GridCell, targetCell: GridCell, scheduleData: ScheduleGridData): Boolean {
    return try {
        Log.d("SubjectAssignmentGrid", "Performing swap between (${selectedCell.dayIndex},${selectedCell.slotIndex}) and (${targetCell.dayIndex},${targetCell.slotIndex})")

        val db = FirebaseFirestore.getInstance()

        // Find the schedule document ID from the school name
        val scheduleQuery = db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .whereEqualTo("name", scheduleData.referenceData.schoolName)
            .limit(1)
            .get()
            .await()

        if (scheduleQuery.documents.isEmpty()) {
            Log.e("SubjectAssignmentGrid", "Schedule document not found")
            return false
        }

        val scheduleDoc = scheduleQuery.documents.first()
        val scheduleId = scheduleDoc.id

        // Get current assignments
        val currentData = scheduleDoc.data ?: return false
        val assignments = mutableListOf<OptimizedVolunteerAssignment>()

        // Parse current assignments (handle both flat and grouped structures)
        val flatAssignments = currentData["optimizedAssignments"] as? List<Map<String, Any>>
        if (flatAssignments != null) {
            assignments.addAll(flatAssignments.map { assignment ->
                OptimizedVolunteerAssignment(
                    volunteerName = assignment["volunteerName"] as? String ?: "",
                    volunteerRollNo = assignment["volunteerRollNo"] as? String ?: assignment["volunteerRollNumber"] as? String ?: "",
                    volunteerGroup = assignment["volunteerGroup"] as? String ?: "",
                    dayIndex = (assignment["dayIndex"] as? Number)?.toInt() ?: 0,
                    slotIndex = (assignment["slotIndex"] as? Number)?.toInt() ?: 0,
                    interviewScore = (assignment["interviewScore"] as? Number)?.toInt() ?: 0,
                    subjectPreference1 = assignment["subjectPreference1"] as? String ?: "",
                    subjectPreference2 = assignment["subjectPreference2"] as? String ?: "",
                    subjectPreference3 = assignment["subjectPreference3"] as? String ?: ""
                )
            })
        }

        // Find and swap the assignments
        val selectedAssignmentIndex = assignments.indexOfFirst {
            it.dayIndex == selectedCell.dayIndex && it.slotIndex == selectedCell.slotIndex
        }
        val targetAssignmentIndex = assignments.indexOfFirst {
            it.dayIndex == targetCell.dayIndex && it.slotIndex == targetCell.slotIndex
        }

        if (selectedAssignmentIndex == -1 || targetAssignmentIndex == -1) {
            Log.e("SubjectAssignmentGrid", "Could not find assignments to swap")
            return false
        }

        // Perform the swap
        val selectedAssignment = assignments[selectedAssignmentIndex]
        val targetAssignment = assignments[targetAssignmentIndex]

        assignments[selectedAssignmentIndex] = selectedAssignment.copy(
            volunteerName = targetAssignment.volunteerName,
            volunteerRollNo = targetAssignment.volunteerRollNo,
            volunteerGroup = targetAssignment.volunteerGroup
        )

        assignments[targetAssignmentIndex] = targetAssignment.copy(
            volunteerName = selectedAssignment.volunteerName,
            volunteerRollNo = selectedAssignment.volunteerRollNo,
            volunteerGroup = selectedAssignment.volunteerGroup
        )

        // Convert back to Firebase format
        val updatedAssignments = assignments.map { assignment ->
            mapOf(
                "volunteerName" to assignment.volunteerName,
                "volunteerRollNo" to assignment.volunteerRollNo,
                "volunteerGroup" to assignment.volunteerGroup,
                "dayIndex" to assignment.dayIndex,
                "slotIndex" to assignment.slotIndex
            )
        }

        // Update Firebase
        db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .document(scheduleId)
            .update("optimizedAssignments", updatedAssignments)
            .await()

        Log.d("SubjectAssignmentGrid", "Swap completed successfully")
        true
    } catch (e: Exception) {
        Log.e("SubjectAssignmentGrid", "Error performing swap", e)
        false
    }
}

// Enhanced function for inter-schedule volunteer swapping
private suspend fun performVolunteerSwapAcrossSchedules(
    selectedCell: GridCell,
    targetCell: GridCell,
    allScheduleData: List<ScheduleGridData>
): Boolean {
    return try {
        Log.d("SubjectAssignmentGrid", "Performing inter-schedule swap between ${selectedCell.scheduleName}(${selectedCell.dayIndex},${selectedCell.slotIndex}) and ${targetCell.scheduleName}(${targetCell.dayIndex},${targetCell.slotIndex})")

        val db = FirebaseFirestore.getInstance()

        // Find both schedule documents
        val selectedScheduleQuery = db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .whereEqualTo("name", selectedCell.scheduleName)
            .limit(1)
            .get()
            .await()

        val targetScheduleQuery = db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .whereEqualTo("name", targetCell.scheduleName)
            .limit(1)
            .get()
            .await()

        if (selectedScheduleQuery.documents.isEmpty() || targetScheduleQuery.documents.isEmpty()) {
            Log.e("SubjectAssignmentGrid", "One or both schedule documents not found")
            return false
        }

        val selectedScheduleDoc = selectedScheduleQuery.documents.first()
        val targetScheduleDoc = targetScheduleQuery.documents.first()
        val selectedScheduleId = selectedScheduleDoc.id
        val targetScheduleId = targetScheduleDoc.id

        // If it's the same schedule, use the original function
        if (selectedScheduleId == targetScheduleId) {
            val scheduleData = allScheduleData.find { it.referenceData.schoolName == selectedCell.scheduleName }
            return if (scheduleData != null) {
                performVolunteerSwap(selectedCell, targetCell, scheduleData)
            } else {
                false
            }
        }

        // Get current assignments for both schedules
        val selectedScheduleData = selectedScheduleDoc.data ?: return false
        val targetScheduleData = targetScheduleDoc.data ?: return false

        // Parse assignments for selected schedule
        val selectedAssignments = mutableListOf<OptimizedVolunteerAssignment>()
        val selectedFlatAssignments = selectedScheduleData["optimizedAssignments"] as? List<Map<String, Any>>
        if (selectedFlatAssignments != null) {
            selectedAssignments.addAll(selectedFlatAssignments.map { assignment ->
                OptimizedVolunteerAssignment(
                    volunteerName = assignment["volunteerName"] as? String ?: "",
                    volunteerRollNo = assignment["volunteerRollNo"] as? String ?: assignment["volunteerRollNumber"] as? String ?: "",
                    volunteerGroup = assignment["volunteerGroup"] as? String ?: "",
                    dayIndex = (assignment["dayIndex"] as? Number)?.toInt() ?: 0,
                    slotIndex = (assignment["slotIndex"] as? Number)?.toInt() ?: 0,
                    interviewScore = (assignment["interviewScore"] as? Number)?.toInt() ?: 0,
                    subjectPreference1 = assignment["subjectPreference1"] as? String ?: "",
                    subjectPreference2 = assignment["subjectPreference2"] as? String ?: "",
                    subjectPreference3 = assignment["subjectPreference3"] as? String ?: ""
                )
            })
        }

        // Parse assignments for target schedule
        val targetAssignments = mutableListOf<OptimizedVolunteerAssignment>()
        val targetFlatAssignments = targetScheduleData["optimizedAssignments"] as? List<Map<String, Any>>
        if (targetFlatAssignments != null) {
            targetAssignments.addAll(targetFlatAssignments.map { assignment ->
                OptimizedVolunteerAssignment(
                    volunteerName = assignment["volunteerName"] as? String ?: "",
                    volunteerRollNo = assignment["volunteerRollNo"] as? String ?: assignment["volunteerRollNumber"] as? String ?: "",
                    volunteerGroup = assignment["volunteerGroup"] as? String ?: "",
                    dayIndex = (assignment["dayIndex"] as? Number)?.toInt() ?: 0,
                    slotIndex = (assignment["slotIndex"] as? Number)?.toInt() ?: 0,
                    interviewScore = (assignment["interviewScore"] as? Number)?.toInt() ?: 0,
                    subjectPreference1 = assignment["subjectPreference1"] as? String ?: "",
                    subjectPreference2 = assignment["subjectPreference2"] as? String ?: "",
                    subjectPreference3 = assignment["subjectPreference3"] as? String ?: ""
                )
            })
        }

        // Find the assignments to swap
        val selectedAssignmentIndex = selectedAssignments.indexOfFirst {
            it.dayIndex == selectedCell.dayIndex && it.slotIndex == selectedCell.slotIndex
        }
        val targetAssignmentIndex = targetAssignments.indexOfFirst {
            it.dayIndex == targetCell.dayIndex && it.slotIndex == targetCell.slotIndex
        }

        if (selectedAssignmentIndex == -1 || targetAssignmentIndex == -1) {
            Log.e("SubjectAssignmentGrid", "Could not find assignments to swap")
            return false
        }

        // Get the assignments to swap
        val selectedAssignment = selectedAssignments[selectedAssignmentIndex]
        val targetAssignment = targetAssignments[targetAssignmentIndex]

        // Perform the swap
        selectedAssignments[selectedAssignmentIndex] = selectedAssignment.copy(
            volunteerName = targetAssignment.volunteerName,
            volunteerRollNo = targetAssignment.volunteerRollNo,
            volunteerGroup = targetAssignment.volunteerGroup,
            interviewScore = targetAssignment.interviewScore,
            subjectPreference1 = targetAssignment.subjectPreference1,
            subjectPreference2 = targetAssignment.subjectPreference2,
            subjectPreference3 = targetAssignment.subjectPreference3
        )

        targetAssignments[targetAssignmentIndex] = targetAssignment.copy(
            volunteerName = selectedAssignment.volunteerName,
            volunteerRollNo = selectedAssignment.volunteerRollNo,
            volunteerGroup = selectedAssignment.volunteerGroup,
            interviewScore = selectedAssignment.interviewScore,
            subjectPreference1 = selectedAssignment.subjectPreference1,
            subjectPreference2 = selectedAssignment.subjectPreference2,
            subjectPreference3 = selectedAssignment.subjectPreference3
        )

        // Convert back to Firebase format
        val updatedSelectedAssignments = selectedAssignments.map { assignment ->
            mapOf(
                "volunteerName" to assignment.volunteerName,
                "volunteerRollNo" to assignment.volunteerRollNo,
                "volunteerGroup" to assignment.volunteerGroup,
                "dayIndex" to assignment.dayIndex,
                "slotIndex" to assignment.slotIndex,
                "interviewScore" to assignment.interviewScore,
                "subjectPreference1" to assignment.subjectPreference1,
                "subjectPreference2" to assignment.subjectPreference2,
                "subjectPreference3" to assignment.subjectPreference3
            )
        }

        val updatedTargetAssignments = targetAssignments.map { assignment ->
            mapOf(
                "volunteerName" to assignment.volunteerName,
                "volunteerRollNo" to assignment.volunteerRollNo,
                "volunteerGroup" to assignment.volunteerGroup,
                "dayIndex" to assignment.dayIndex,
                "slotIndex" to assignment.slotIndex,
                "interviewScore" to assignment.interviewScore,
                "subjectPreference1" to assignment.subjectPreference1,
                "subjectPreference2" to assignment.subjectPreference2,
                "subjectPreference3" to assignment.subjectPreference3
            )
        }

        // Update both Firebase documents
        db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .document(selectedScheduleId)
            .update("optimizedAssignments", updatedSelectedAssignments)
            .await()

        db.collection(FirestoreCollection.GENERATED_SCHEDULES)
            .document(targetScheduleId)
            .update("optimizedAssignments", updatedTargetAssignments)
            .await()

        Log.d("SubjectAssignmentGrid", "Inter-schedule swap completed successfully")
        true
    } catch (e: Exception) {
        Log.e("SubjectAssignmentGrid", "Error performing inter-schedule swap", e)
        false
    }
}

@Composable
fun VolunteerDetailsDialog(
    cell: GridCell,
    scheduleData: ScheduleGridData?,
    onDismiss: () -> Unit
) {
    // No async loading needed - all data is already available in GridCell from generateSchedule collection

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = if (cell.volunteerName.isNotEmpty()) "Volunteer Details" else "Empty Slot Details",
                color = YellowAccent,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                if (cell.volunteerName.isNotEmpty()) {
                    // Assigned volunteer details - all data immediately available from GridCell
                    DetailRow("Volunteer Name", cell.volunteerName)
                    DetailRow("Roll Number", cell.volunteerRollNo)
                    DetailRow("Group", cell.volunteerGroup)

                    // Interview score - immediately available from generateSchedule collection data
                    if (cell.interviewScore > 0) {
                        DetailRow("Interview Score", "${cell.interviewScore}/100")
                    } else {
                        DetailRow("Interview Score", "Not available")
                    }

                    // Subject preferences section - immediately available from generateSchedule collection data
                    if (cell.subjectPreference1.isNotEmpty() ||
                        cell.subjectPreference2.isNotEmpty() ||
                        cell.subjectPreference3.isNotEmpty()) {

                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Subject Preferences:",
                            color = YellowAccent,
                            fontWeight = FontWeight.Medium,
                            style = MaterialTheme.typography.bodyMedium
                        )

                        if (cell.subjectPreference1.isNotEmpty()) {
                            DetailRow("1st Preference", cell.subjectPreference1)
                        }
                        if (cell.subjectPreference2.isNotEmpty()) {
                            DetailRow("2nd Preference", cell.subjectPreference2)
                        }
                        if (cell.subjectPreference3.isNotEmpty()) {
                            DetailRow("3rd Preference", cell.subjectPreference3)
                        }
                    } else {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Subject Preferences:",
                            color = YellowAccent,
                            fontWeight = FontWeight.Medium,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "Not available",
                            color = Color(0xFFB0B0B0),
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                } else {
                    // Empty slot details
                    Text(
                        text = "No volunteer assigned to this slot",
                        color = Color(0xFFB0B0B0),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }

                // Available groups for this time slot
                scheduleData?.let { data ->
                    val availableGroups = data.groupAvailability
                        .find { it.dayIndex == cell.dayIndex && it.slotIndex == cell.slotIndex }
                        ?.availableGroups ?: emptyList()

                    if (availableGroups.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Available Groups:",
                            color = YellowAccent,
                            fontWeight = FontWeight.Medium,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = availableGroups.joinToString(", "),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }

                // Subject assignment (if any)
                cell.subjectCode?.let { subject ->
                    Spacer(modifier = Modifier.height(8.dp))
                    DetailRow("Assigned Subject", subject)
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(contentColor = YellowAccent)
            ) {
                Text("Close")
            }
        },
        containerColor = Color(0xFF1E1E1E),
        textContentColor = Color.White
    )
}

@Composable
private fun DetailRow(label: String, value: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = "$label:",
            color = Color(0xFFB0B0B0),
            style = MaterialTheme.typography.bodyMedium
        )
        Text(
            text = value,
            color = Color.White,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}



// Note: loadVolunteerDetails function removed - volunteer data is now immediately available
// from GridCell which contains enhanced data from generateSchedule collection
