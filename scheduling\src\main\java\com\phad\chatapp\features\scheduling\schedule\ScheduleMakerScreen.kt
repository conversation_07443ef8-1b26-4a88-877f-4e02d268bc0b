package com.phad.chatapp.features.scheduling.schedule

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.filled.ViewList
import androidx.compose.material.icons.outlined.Assignment
import androidx.compose.material.icons.outlined.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color

import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.phad.chatapp.features.scheduling.ui.theme.*
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScheduleMakerScreen(navController: NavController) {
    // For now, always enable animations. In a real app, you'd check accessibility settings
    val animationsEnabled = true

    // Animation states
    var isVisible by remember { mutableStateOf(false) }
    var headerVisible by remember { mutableStateOf(false) }
    var buttonsVisible by remember { mutableStateOf(false) }

    // Start animations on composition
    LaunchedEffect(Unit) {
        if (animationsEnabled) {
            delay(100)
            headerVisible = true
            delay(200)
            buttonsVisible = true
            delay(100)
            isVisible = true
        } else {
            // Show everything immediately if animations are disabled
            headerVisible = true
            buttonsVisible = true
            isVisible = true
        }
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = DarkBackground
    ) {
        AnimatedVisibility(
            visible = isVisible,
            enter = slideInVertically(
                initialOffsetY = { if (animationsEnabled) it / 4 else 0 },
                animationSpec = tween(durationMillis = if (animationsEnabled) 400 else 0)
            ) + fadeIn(
                animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 20.dp, vertical = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Animated Header Section
                AnimatedVisibility(
                    visible = headerVisible,
                    enter = fadeIn(
                        animationSpec = tween(
                            durationMillis = if (animationsEnabled) 500 else 0,
                            delayMillis = if (animationsEnabled) 100 else 0
                        )
                    )
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(bottom = 32.dp)
                    ) {
                        Text(
                            text = "Schedule Maker",
                            style = MaterialTheme.typography.displayLarge,
                            color = MaterialTheme.colorScheme.onBackground,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        Text(
                            text = "Teaching & Technical Wing",
                            style = MaterialTheme.typography.titleLarge,
                            color = MaterialTheme.colorScheme.onBackground,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(bottom = 24.dp)
                        )

                        Text(
                            text = "What would you like to do?",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = TextAlign.Center
                        )
                    }
                }

                // Animated Menu Buttons with staggered entrance
                AnimatedVisibility(
                    visible = buttonsVisible,
                    enter = slideInVertically(
                        initialOffsetY = { if (animationsEnabled) it / 3 else 0 },
                        animationSpec = tween(durationMillis = if (animationsEnabled) 400 else 0)
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
                    )
                ) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // Staggered menu buttons
                        StaggeredMenuButton(
                            delay = if (animationsEnabled) 0 else 0,
                            animationsEnabled = animationsEnabled,
                            icon = Icons.Default.Schedule,
                            title = "Create Teaching Slots",
                            description = "Define slots for teaching sessions",
                            onClick = { navController.navigate("teachingSlots") }
                        )

                        StaggeredMenuButton(
                            delay = if (animationsEnabled) 100 else 0,
                            animationsEnabled = animationsEnabled,
                            icon = Icons.Default.DateRange,
                            title = "Set Availability",
                            description = "Add volunteer's free time slots for scheduling",
                            onClick = {
                                navController.navigate("teachingSlotsOptionsScreen?destination=setAvailability")
                            }
                        )

                        StaggeredMenuButton(
                            delay = if (animationsEnabled) 200 else 0,
                            animationsEnabled = animationsEnabled,
                            icon = Icons.Default.Group,
                            title = "Manage Volunteers",
                            description = "Select volunteers for teaching sessions",
                            onClick = { navController.navigate("volunteerPresets") }
                        )

                        StaggeredMenuButton(
                            delay = if (animationsEnabled) 300 else 0,
                            animationsEnabled = animationsEnabled,
                            icon = Icons.Default.ViewList,
                            title = "Generate Schedule",
                            description = "Create complete teaching schedule",
                            onClick = { navController.navigate("scheduleGeneration") }
                        )

                        StaggeredMenuButton(
                            delay = if (animationsEnabled) 400 else 0,
                            animationsEnabled = animationsEnabled,
                            icon = Icons.Default.School,
                            title = "Assign Subject",
                            description = "Assign subjects to sessions",
                            onClick = { navController.navigate("subjectAssignment") }
                        )

                        // Compact special button
                        StaggeredCompactAssignmentButton(
                            delay = if (animationsEnabled) 500 else 0,
                            animationsEnabled = animationsEnabled,
                            onClick = {
                                // Placeholder for future navigation - UI only for now
                            }
                        )
                    }
                }

                // Add bottom padding to prevent overlap with navigation bar
                Spacer(modifier = Modifier.height(100.dp))
            }
        }
    }
}

// Staggered Menu Button with entrance animation
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StaggeredMenuButton(
    delay: Int,
    animationsEnabled: Boolean,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    description: String,
    onClick: () -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 100),
        label = "button_scale"
    )

    LaunchedEffect(Unit) {
        if (animationsEnabled) {
            delay(delay.toLong())
        }
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
            animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
        ) + fadeIn(
            animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
        )
    ) {
        Card(
            onClick = onClick,
            modifier = Modifier
                .fillMaxWidth()
                .scale(scale),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = NeutralCardSurface // Using DarkBackground theme color (#121212)
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp,
                pressedElevation = 2.dp
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                // Circular yellow icon background - restored original styling
                Box(
                    modifier = Modifier
                        .size(56.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFFFD600)) // Yellow background
                        .padding(12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = Color.Black,
                        modifier = Modifier.size(24.dp)
                    )
                }

                // Text column with title and description - updated for dark background
                Column(
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .weight(1f)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        color = Color.White // White text for dark background
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFFB0B0B0) // Light grey for description
                    )
                }
            }
        }
    }
}

// Compact Assignment Button - smaller and positioned as secondary action
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StaggeredCompactAssignmentButton(
    delay: Int,
    animationsEnabled: Boolean,
    onClick: () -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 100),
        label = "compact_assignment_scale"
    )

    LaunchedEffect(Unit) {
        if (animationsEnabled) {
            delay(delay.toLong())
        }
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
            animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
        ) + fadeIn(
            animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
        )
    ) {
        // Compact button with reduced size and subtle styling
        OutlinedCard(
            onClick = onClick,
            modifier = Modifier
                .fillMaxWidth()
                .scale(scale),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.outlinedCardColors(
                containerColor = Color(0xFF1A1A1A)
            ),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = Color(0xFF4CAF50)
            ),
            elevation = CardDefaults.outlinedCardElevation(
                defaultElevation = 2.dp
            )
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .padding(12.dp)
                    .fillMaxWidth()
            ) {
                // Smaller icon with green accent
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF4CAF50).copy(alpha = 0.15f))
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Outlined.Assignment,
                        contentDescription = null,
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(20.dp)
                    )
                }

                Spacer(modifier = Modifier.width(12.dp))

                // Compact text
                Text(
                    text = "View Assignments",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF4CAF50),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
fun ScheduleMakerScreenPreview() {
    SchedulingTheme {
        val navController = androidx.navigation.compose.rememberNavController()
        ScheduleMakerScreen(navController = navController)
    }
}

@Composable
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
fun StaggeredMenuButtonPreview() {
    SchedulingTheme {
        Surface(
            modifier = Modifier.padding(16.dp),
            color = DarkBackground
        ) {
            StaggeredMenuButton(
                delay = 0,
                animationsEnabled = false,
                icon = Icons.Default.Schedule,
                title = "Create Teaching Slots",
                description = "Define slots for teaching sessions",
                onClick = { }
            )
        }
    }
}

@Composable
@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
fun StaggeredCompactAssignmentButtonPreview() {
    SchedulingTheme {
        Surface(
            modifier = Modifier.padding(16.dp),
            color = DarkBackground
        ) {
            StaggeredCompactAssignmentButton(
                delay = 0,
                animationsEnabled = false,
                onClick = { }
            )
        }
    }
}